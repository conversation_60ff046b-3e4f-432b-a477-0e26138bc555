<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Log;

class SetLocale
{
    protected $allowedLocales = ['en', 'bg'];

    public function handle(Request $request, Closure $next)
    {
        // Първо проверяваме за локал в сесията
        $locale = Session::get('locale');
        Log::debug('Locale from session: ' . ($locale ?? 'null'));
        
        // Ако няма в сесията и има логнат потребител, вземаме от базата
        if ((!$locale || !in_array($locale, $this->allowedLocales)) && Auth::check()) {
            $locale = Auth::user()->locale;
            Log::debug('Locale from database: ' . ($locale ?? 'null'));
            
            if ($locale && in_array($locale, $this->allowedLocales)) {
                Session::put('locale', $locale);
            }
        }
        
        // Ако все още няма валиден локал, задаваме български по подразбиране
        if (!$locale || !in_array($locale, $this->allowedLocales)) {
            $locale = 'bg';
            Log::debug('Using hardcoded default locale: bg');
            
            if (Auth::check()) {
                Auth::user()->update(['locale' => $locale]);
            }
            Session::put('locale', $locale);
        }

        App::setLocale($locale);
        Log::debug('Final locale set to: ' . App::getLocale());
        
        return $next($request);
    }
} 